export interface AuthConfig {
  type: 'header' | 'query' | 'custom_headers';
  headerName?: string;
  valuePrefix?: string;
  keyParamName?: string;
  apiKeyEnvVar?: string;
  customHeaders?: Record<string, { envVar?: string; value?: string }>;
}

export interface ParsingConfig {
  modelListPath?: string;
  modelNamePath: string;
  modelNameRegex?: string;
}

export interface IconConfig {
  slug?: string;
  format?: 'svg' | 'png' | 'webp';
  theme?: 'light' | 'dark';
  size?: number;
}

export interface ProviderConfig {
  id: string;
  name: string;
  enabled: boolean;
  url: string;
  method: string;
  auth: AuthConfig;
  parsing: ParsingConfig;
  icon?: IconConfig;
}

export interface NotificationConfig {
  id: string;
  enabled: boolean;
  type: string;
  webhookUrlEnvVar: string;
  triggerOn: string[];
  requestBodyTemplate: any;
}

export interface GlobalIconSettings {
  enabled: boolean;
  cdnSource: 'unpkg' | 'npmmirror';
  format: 'svg' | 'png' | 'webp';
  theme: 'light' | 'dark';
  size: number;
  fallbackIcon: string;
}

export interface FrontendSettings {
  title: string;
  faviconUrl?: string;
  backgroundImageUrl?: string;
  backgroundOpacity?: number;
  modelCopySeparator?: string;
}

export interface Config {
  checkIntervalSeconds: number;
  notifications: NotificationConfig[];
  frontendSettings: FrontendSettings;
  iconSettings?: GlobalIconSettings;
  providers: ProviderConfig[];
}

export async function loadConfig(): Promise<Config> {
  const configFile = Bun.file('config.json');
  const configText = await configFile.text();
  const config = JSON.parse(configText) as Config;

  // Validate required fields
  if (!config.checkIntervalSeconds || config.checkIntervalSeconds < 60) {
    throw new Error('checkIntervalSeconds must be at least 60 seconds');
  }

  if (!config.providers || config.providers.length === 0) {
    throw new Error('At least one provider must be configured');
  }

  // Set defaults
  config.frontendSettings = {
    ...{
      title: 'ModelSentry - AI 模型监控',
      faviconUrl: '/static/favicon.ico',
      backgroundImageUrl: '/static/background.jpg',
      backgroundOpacity: 0.7,
      modelCopySeparator: ','
    },
    ...config.frontendSettings
  };

  config.iconSettings = {
    ...{
      enabled: true,
      cdnSource: 'unpkg' as const,
      format: 'svg' as const,
      theme: 'light' as const,
      size: 32,
      fallbackIcon: 'ai'
    },
    ...config.iconSettings
  };

  return config;
}

export function getEnvVar(envVarName: string): string | undefined {
  return process.env[envVarName];
}
