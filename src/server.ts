import { loadConfig, Config } from './config.ts';
import { ProviderManager } from './providers.ts';
import { NotificationManager } from './notifications.ts';
import { ModelMonitor } from './monitor.ts';
import { IconManager } from './icons.ts';
import { log } from './logger.ts';

class ModelSentryServer {
  private config!: Config;
  private providerManager!: ProviderManager;
  private notificationManager!: NotificationManager;
  private monitor!: ModelMonitor;
  private iconManager!: IconManager;
  private server!: any;

  async initialize(): Promise<void> {
    try {
      log.time('initialization');

      log.info('Loading configuration');
      this.config = await loadConfig();

      log.info('Initializing components', {
        providers: this.config.providers.length,
        notifications: this.config.notifications.length
      });

      this.providerManager = new ProviderManager(this.config.providers);
      this.notificationManager = new NotificationManager(this.config.notifications);
      this.iconManager = new IconManager(this.config.iconSettings!);
      this.monitor = new ModelMonitor(this.config, this.providerManager, this.notificationManager);

      await this.startServer();
      this.monitor.start();

      // Send startup test notification
      await this.sendStartupTestNotification();

      log.timeEnd('initialization');
      log.startup('ModelSentry initialized successfully');
    } catch (error) {
      log.error('Failed to initialize ModelSentry', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  }

  private async startServer(): Promise<void> {
    const port = parseInt(process.env.PORT || '3000');

    try {
      this.server = Bun.serve({
        port,
        fetch: async (req: Request) => {
          const url = new URL(req.url);

          if (url.pathname === '/') {
            return await this.handleHomePage();
          } else if (url.pathname.startsWith('/static/')) {
            return await this.handleStaticFile(url.pathname);
          } else if (url.pathname === '/api/status') {
            return await this.handleApiStatus();
          } else if (url.pathname === '/api/force-check') {
            return await this.handleForceCheck();
          }

          return new Response('Not Found', { status: 404 });
        }
      });

      log.startup(`Server running on http://localhost:${port}`, { port });
    } catch (error) {
      throw new Error(`Failed to start server. Is port ${port} in use?`);
    }
  }

  private async handleHomePage(): Promise<Response> {
    try {
      const templateFile = Bun.file('src/templates/index.html');
      let template = await templateFile.text();
      
      const status = this.monitor.getStatus();
      const templateData = this.buildTemplateData(status);
      
      const html = this.renderTemplate(template, templateData);
      
      return new Response(html, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    } catch (error) {
      log.error('Error rendering home page', error instanceof Error ? error.message : String(error));
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async handleStaticFile(pathname: string): Promise<Response> {
    try {
      const filePath = pathname.replace('/static/', 'static/');
      const file = Bun.file(filePath);
      
      if (await file.exists()) {
        const contentType = this.getContentType(filePath);
        return new Response(file, {
          headers: { 'Content-Type': contentType }
        });
      }
      
      // Fallback for default files
      if (pathname === '/static/favicon.ico') {
        return this.generateEmojiIcon('🤗');
      }
      
      return new Response('Not Found', { status: 404 });
    } catch (error) {
      log.error('Error serving static file', error instanceof Error ? error.message : String(error), { pathname });
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async handleApiStatus(): Promise<Response> {
    try {
      const status = this.monitor.getStatus();
      return new Response(JSON.stringify(status), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      log.error('Error getting status', error instanceof Error ? error.message : String(error));
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async handleForceCheck(): Promise<Response> {
    try {
      await this.monitor.forceCheck();
      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      log.error('Error forcing check', error instanceof Error ? error.message : String(error));
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private buildTemplateData(status: any): any {
    const enabledProviders = status.providers.filter((p: any) => p.enabled);
    
    return {
      title: this.config.frontendSettings.title,
      faviconUrl: this.config.frontendSettings.faviconUrl || '/static/favicon.ico',
      backgroundCss: this.getBackgroundCss(),
      backgroundClass: this.config.frontendSettings.backgroundImageUrl ? '' : 'animated-bg',
      backgroundOpacity: this.config.frontendSettings.backgroundOpacity || 0.7,
      modelCopySeparator: this.config.frontendSettings.modelCopySeparator || ',',
      totalProviders: status.providers.length,
      enabledProviders: enabledProviders.length,
      providers: status.providers.map((provider: any) => {
        const isMissingKey = !provider.enabled && provider.error === 'Missing API key';
        const providerConfig = this.config.providers.find(p => p.id === provider.id);

        // Generate icon HTML for the provider
        const iconHtml = this.iconManager.generateIconHtml(provider.name, providerConfig?.icon);
        const iconUrl = this.iconManager.generateIconUrl(provider.name, providerConfig?.icon);

        return {
          ...provider,
          lastCheck: provider.lastCheck ? this.formatDate(provider.lastCheck) : null,
          lastSuccess: provider.lastSuccess ? this.formatDate(provider.lastSuccess) : null,
          nextRetryAt: provider.nextRetryAt ? this.formatDate(provider.nextRetryAt) : null,
          isMissingKey,
          requiredEnvVar: isMissingKey ? providerConfig?.auth.apiKeyEnvVar : null,
          iconHtml,
          iconUrl,
          models: provider.models ? provider.models.map((model: any) => ({
            ...model,
            id: provider.id,  // Add provider ID to each model
            isNew: this.monitor.isNewModel(provider.id, model.name)  // Check if model is new
          })) : []
        };
      })
    };
  }

  private getBackgroundCss(): string {
    if (this.config.frontendSettings.backgroundImageUrl) {
      return `url('${this.config.frontendSettings.backgroundImageUrl}')`;
    }
    return 'linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)';
  }

  private renderTemplate(template: string, data: any): string {
    // Simple Mustache-like template rendering
    let rendered = template;

    // Handle arrays first (they use {{#arrayName}} syntax)
    rendered = this.renderArrays(rendered, data);

    // Handle conditional sections (they use {{#condition}} and {{^condition}} syntax)
    rendered = this.renderConditionals(rendered, data);

    // Replace triple-brace variables first (unescaped HTML)
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string' || typeof value === 'number') {
        rendered = rendered.replace(new RegExp(`{{{${key}}}}`, 'g'), String(value));
      }
    }

    // Replace simple variables last
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string' || typeof value === 'number') {
        rendered = rendered.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      }
    }

    return rendered;
  }

  private renderConditionals(template: string, data: any): string {
    // Handle {{#condition}}...{{/condition}} and {{^condition}}...{{/condition}}
    // Only process non-array conditions
    const conditionalRegex = /{{([#^])(\w+)}}([\s\S]*?){{\/\2}}/g;

    return template.replace(conditionalRegex, (match, type, key, content) => {
      const value = data[key];

      // Skip if this is an array (arrays are handled separately)
      if (Array.isArray(value)) {
        return match;
      }

      const shouldShow = type === '#' ? !!value : !value;

      if (shouldShow) {
        return this.renderTemplate(content, data);
      }
      return '';
    });
  }

  private renderArrays(template: string, data: any): string {
    // Handle {{#arrayName}}...{{/arrayName}} for arrays
    const arrayRegex = /{{#(\w+)}}([\s\S]*?){{\/\1}}/g;

    return template.replace(arrayRegex, (match, key, content) => {
      const array = data[key];
      if (Array.isArray(array)) {
        return array.map(item => {
          // Merge parent data with item data, item data takes precedence
          const mergedData = { ...data, ...item };
          return this.renderTemplate(content, mergedData);
        }).join('');
      }
      return match; // Return unchanged if not an array
    });
  }

  private formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  private getContentType(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    const types: Record<string, string> = {
      'html': 'text/html',
      'css': 'text/css',
      'js': 'application/javascript',
      'json': 'application/json',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'ico': 'image/x-icon'
    };
    return types[ext || ''] || 'application/octet-stream';
  }

  private generateEmojiIcon(emoji: string): Response {
    const svg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
      <text y=".9em" font-size="90">${emoji}</text>
    </svg>`;
    
    return new Response(svg, {
      headers: { 'Content-Type': 'image/svg+xml' }
    });
  }

  private async sendStartupTestNotification(): Promise<void> {
    try {
      // Create a welcome message for startup test
      const welcomeChange = {
        providerId: 'system',
        providerName: 'ModelSentry 系统',
        added: [{ name: '🎉 ModelSentry 已成功启动！欢迎使用模型监控服务', id: 'startup-test' }],
        removed: [],
        error: undefined
      };

      log.info('Sending startup test notification');
      // Temporarily bypass grace period for startup notification
      await this.notificationManager.sendStartupNotification(welcomeChange);
      log.info('Startup test notification sent successfully');
    } catch (error) {
      log.warn('Failed to send startup test notification', { error: error instanceof Error ? error.message : String(error) });
      // Don't fail the startup process if notification fails
    }
  }

  async shutdown(): Promise<void> {
    log.shutdown('Shutting down ModelSentry');

    if (this.monitor) {
      this.monitor.stop();
    }

    if (this.server) {
      this.server.stop();
    }

    log.shutdown('ModelSentry shutdown complete');
  }
}

// Handle graceful shutdown
const server = new ModelSentryServer();

process.on('SIGINT', async () => {
  await server.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await server.shutdown();
  process.exit(0);
});

// Start the server
server.initialize().catch(error => {
  log.error('Failed to start ModelSentry', error instanceof Error ? error.message : String(error));
  process.exit(1);
});
