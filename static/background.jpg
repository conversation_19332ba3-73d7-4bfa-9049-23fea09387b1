<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080">
  <defs>
    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bg2" x1="0%" y1="100%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="bg3" x1="50%" y1="0%" x2="50%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- Base gradient -->
  <rect width="100%" height="100%" fill="url(#bg1)"/>
  
  <!-- Overlay gradients -->
  <rect width="100%" height="100%" fill="url(#bg2)"/>
  <rect width="100%" height="100%" fill="url(#bg3)"/>
  
  <!-- Decorative circles -->
  <circle cx="200" cy="200" r="100" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1720" cy="300" r="150" fill="rgba(255,255,255,0.08)"/>
  <circle cx="300" cy="800" r="80" fill="rgba(255,255,255,0.12)"/>
  <circle cx="1600" cy="900" r="120" fill="rgba(255,255,255,0.06)"/>
  
  <!-- Subtle pattern -->
  <g opacity="0.1">
    <pattern id="dots" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
      <circle cx="25" cy="25" r="2" fill="white"/>
    </pattern>
    <rect width="100%" height="100%" fill="url(#dots)"/>
  </g>
</svg>
