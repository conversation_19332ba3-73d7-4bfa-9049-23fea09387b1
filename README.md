# ModelSentry

轻量级 AI 模型监控工具，实时跟踪多个 AI 服务提供商的模型列表变化，通过 Webhook 发送通知。

## 特性

- 🔍 **实时监控**: 定时检查多个 AI 提供商的模型列表
- 📱 **Webhook 通知**: 支持企业微信、飞书等通知方式
- 🚀 **无状态设计**: 无需外部数据库，状态存储在内存中
- 🎨 **美观界面**: 现代化 Web 界面，支持自定义背景
- 🐳 **容器化**: Docker 支持，便于部署
- ⚡ **高性能**: 基于 Bun 运行时，启动快速
- 🔑 **智能配置**: 自动识别缺少 API Key 的提供商
- 🛡️ **优雅重启**: 2分钟通知宽限期，防止误报警

## 支持的 AI 提供商

Google Gemini、OpenAI、Anthropic Claude、GroqCloud、Cerebras、Cohere、XAI、Mistral、Sambanova AI、Together AI、360 AI、Siliconflow、BaiChuan、DeepSeek、StepFun

## 快速开始

### 环境要求

- Bun >= 1.0.0

### 安装和运行

```bash
# 安装依赖
bun install

# 设置环境变量
export OPENAI_API_KEY="your-openai-key"
export CLAUDE_API_KEY="your-claude-key"
export GEMINI_API_KEY="your-gemini-key"
export WEWORK_BOT_KEY_CHANGES="your-webhook-url"

# 验证配置
bun run validate

# 运行
bun run start
```

### Docker 部署

#### 标准部署
```bash
docker build -t modelsentry .
docker run -d --name modelsentry -p 3000:3000 \
  -e OPENAI_API_KEY="your-key" \
  -e WEWORK_BOT_KEY_CHANGES="your-webhook" \
  modelsentry
```

#### SlimToolkit 优化部署（推荐）
使用 SlimToolkit 可以将镜像大小减少 39.2%（从 181MB 减少到 110MB）：

```bash
# 手动本地优化（可选）
docker build -t modelsentry:standard .
docker pull dslim/slim
docker run --rm \
  -v /var/run/docker.sock:/var/run/docker.sock \
  dslim/slim build \
  --target modelsentry:standard \
  --tag modelsentry:slim \
  --include-shell \
  --include-cert-all \
  --include-path /app \
  --http-probe \
  --http-probe-ports 3000

# 使用优化后的镜像
docker run -d --name modelsentry -p 3000:3000 \
  -e OPENAI_API_KEY="your-key" \
  -e WEWORK_BOT_KEY_CHANGES="your-webhook" \
  modelsentry:slim
```

#### 预构建镜像
```bash
# 使用 GitHub Container Registry 的预构建镜像
docker pull ghcr.io/mgrsc/modelsentry:latest        # 完整版本 (181MB)
docker pull ghcr.io/mgrsc/modelsentry:slim          # SlimToolkit优化版本 (110MB)

# 推荐使用slim版本
docker run -d --name modelsentry -p 3000:3000 \
  -e OPENAI_API_KEY="your-key" \
  -e WEWORK_BOT_KEY_CHANGES="your-webhook" \
  ghcr.io/mgrsc/modelsentry:slim
```

## 配置

编辑 `config.json` 文件配置提供商和通知设置。主要配置项：

- `checkIntervalSeconds`: 检查间隔（秒）
- `providers`: 提供商配置数组
- `notifications`: 通知配置数组
- `frontendSettings`: 界面设置

### 日志配置

通过环境变量 `LOG_LEVEL` 控制日志级别：

```bash
# 设置日志级别 (DEBUG, INFO, WARN, ERROR)
export LOG_LEVEL=INFO

# 禁用彩色输出
export NO_COLOR=1
```

日志特性：
- 🚀 启动/关闭事件
- 📊 模型变更检测
- 🌐 网络请求详情
- ⏱️ 性能计时
- 结构化上下文信息

详细配置说明请参考 `config.json` 文件中的示例。

## 🔧 Docker 镜像优化

ModelSentry 支持使用 SlimToolkit 进行镜像优化，可以显著减少镜像大小：

### 优化效果
- **镜像大小减少**: 39.2% (181MB → 110MB)
- **拉取时间减少**: 显著提升
- **存储成本降低**: 线性减少
- **功能完整性**: 100% 保持

### 自动化优化
通过 GitHub Actions 自动完成，推送代码即可获得优化镜像。

### CI/CD 集成
项目已集成 SlimToolkit 到 GitHub Actions，自动构建和推送两个版本：
- `latest`: 完整版本 (181MB) - 包含所有工具和依赖
- `slim`: SlimToolkit优化版本 (110MB) - 生产环境推荐

详细说明请参考 [SlimToolkit 优化指南](docs/SLIM_OPTIMIZATION.md)。

## 许可证

MIT License
