# Dependencies (will be installed in container)
node_modules
.bun

# Environment files
.env*
!.env.example

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build outputs (will be built in container)
dist
build
.next
.nuxt

# Cache directories
.cache
.parcel-cache
.nyc_output
coverage
*.tsbuildinfo

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# Test files
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
coverage/

# Development tools config
.eslintrc*
.prettierrc*
jest.config.*
webpack.config.*
rollup.config.*
vite.config.*

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Package manager files (keep package.json, exclude locks for multi-stage)
# bun.lock will be copied separately for caching
