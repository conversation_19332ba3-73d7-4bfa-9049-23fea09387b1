version: '3.8'

services:
  modelsentry:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      cache_from:
        - ghcr.io/mgrsc/modelsentry:latest
    image: modelsentry:latest
    container_name: modelsentry
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - PORT=3000
      # AI Provider API Keys (uncomment and set as needed)
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
      # - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      # - GEMINI_API_KEY=${GEMINI_API_KEY}
      # - GROQ_API_KEY=${GROQ_API_KEY}
      # - CEREBRAS_API_KEY=${CEREBRAS_API_KEY}
      # - COHERE_API_KEY=${COHERE_API_KEY}
      # - XAI_API_KEY=${XAI_API_KEY}
      # - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      # - SAMBANOVA_API_KEY=${SAMBANOVA_API_KEY}
      # - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      # - CN_360_API_KEY=${CN_360_API_KEY}
      # - SILICONFLOW_API_KEY=${SILICONFLOW_API_KEY}
      # - BAICHUAN_API_KEY=${BAICHUAN_API_KEY}
      # - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      # - STEPFUN_API_KEY=${STEPFUN_API_KEY}

      # Webhook URLs (uncomment and set as needed)
      # - WEWORK_BOT_KEY_CHANGES=${WEWORK_BOT_KEY_CHANGES}
      # - LARK_BOT_WEBHOOK_URL=${LARK_BOT_WEBHOOK_URL}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # Mount config file (recommended for production)
      - ./config.json:/app/config.json:ro
      # Optional: Mount custom static files
      # - ./custom-static:/app/static:ro
    networks:
      - modelsentry-network
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

networks:
  modelsentry-network:
    driver: bridge
