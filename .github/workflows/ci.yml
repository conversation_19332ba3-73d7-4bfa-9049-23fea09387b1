name: ModelSentry CI and Docker Build

on:
  push:
    branches: [ main, develop ]

env:
  # Container registry configuration
  # Set CUSTOM_REGISTRY in repository secrets for custom registry (e.g., registry.o00o.men)
  # Set REGISTRY_USERNAME and REGISTRY_PASSWORD for authentication (optional)
  REGISTRY: ${{ secrets.CUSTOM_REGISTRY || 'ghcr.io' }}
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-docker:
    name: Build and Optimize Docker Image
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4.2.2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Get short SHA
      id: sha
      run: echo "short_sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

    - name: Determine registry authentication
      id: registry-auth
      env:
        REGISTRY_USERNAME: ${{ secrets.REGISTRY_USERNAME }}
        REGISTRY_PASSWORD: ${{ secrets.REGISTRY_PASSWORD }}
      run: |
        if [[ "${{ env.REGISTRY }}" == "ghcr.io" ]]; then
          echo "needs-auth=true" >> $GITHUB_OUTPUT
          echo "should-push=true" >> $GITHUB_OUTPUT
          echo "username=${{ github.actor }}" >> $GITHUB_OUTPUT
          echo "password=${{ secrets.GITHUB_TOKEN }}" >> $GITHUB_OUTPUT
          echo "🔐 Using GitHub Container Registry with GitHub token"
        elif [[ -n "${REGISTRY_USERNAME}" && -n "${REGISTRY_PASSWORD}" ]]; then
          echo "needs-auth=true" >> $GITHUB_OUTPUT
          echo "should-push=true" >> $GITHUB_OUTPUT
          echo "username=${REGISTRY_USERNAME}" >> $GITHUB_OUTPUT
          echo "password=${REGISTRY_PASSWORD}" >> $GITHUB_OUTPUT
          echo "🔐 Using custom registry ${{ env.REGISTRY }} with provided credentials"
        else
          echo "needs-auth=false" >> $GITHUB_OUTPUT
          echo "should-push=true" >> $GITHUB_OUTPUT
          echo "📦 Using registry ${{ env.REGISTRY }} without authentication (will attempt to push)"
        fi

    - name: Log in to Container Registry
      if: steps.registry-auth.outputs.needs-auth == 'true'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ steps.registry-auth.outputs.username }}
        password: ${{ steps.registry-auth.outputs.password }}

    - name: Setup image names
      if: steps.registry-auth.outputs.should-push == 'true'
      run: |
        # 将镜像名称转换为小写以符合Docker标准
        LOWERCASE_IMAGE_NAME=$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]')
        echo "LOWERCASE_IMAGE_NAME=${LOWERCASE_IMAGE_NAME}" >> $GITHUB_ENV
        echo "LATEST_IMAGE=${{ env.REGISTRY }}/${LOWERCASE_IMAGE_NAME}:latest" >> $GITHUB_ENV
        echo "SLIM_IMAGE=${{ env.REGISTRY }}/${LOWERCASE_IMAGE_NAME}:slim" >> $GITHUB_ENV

    - name: Build and push standard Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile
        push: ${{ steps.registry-auth.outputs.should-push == 'true' }}
        tags: ${{ env.LATEST_IMAGE }}
        platforms: linux/amd64
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Setup SlimToolkit optimization
      if: steps.registry-auth.outputs.should-push == 'true'
      run: |
        echo "Will optimize image: ${{ env.LATEST_IMAGE }}"

        # 拉取刚推送的标准镜像
        echo "Pulling standard image for optimization..."
        docker pull ${{ env.LATEST_IMAGE }}

        # 拉取SlimToolkit
        echo "Pulling SlimToolkit..."
        docker pull dslim/slim

    - name: Optimize Docker image with SlimToolkit
      if: steps.registry-auth.outputs.should-push == 'true'
      run: |
        echo "Starting SlimToolkit optimization of: ${{ env.LATEST_IMAGE }}"
        docker run --rm \
          -v /var/run/docker.sock:/var/run/docker.sock \
          dslim/slim build \
          --target ${{ env.LATEST_IMAGE }} \
          --tag ${{ env.SLIM_IMAGE }} \
          --include-shell \
          --include-cert-all \
          --include-path /app \
          --include-path /etc/ssl \
          --include-path /usr/share/ca-certificates \
          --include-path /etc/ca-certificates \
          --include-path /tmp \
          --include-workdir \
          --http-probe \
          --http-probe-retry-count 5 \
          --http-probe-retry-wait 10 \
          --http-probe-ports 3000 \
          --continue-after 30

        echo "SlimToolkit optimization completed"

    - name: Push optimized image
      if: steps.registry-auth.outputs.should-push == 'true'
      run: |
        echo "Pushing optimized image: ${{ env.SLIM_IMAGE }}"
        docker push ${{ env.SLIM_IMAGE }}
        echo "✅ Slim image pushed successfully"



    - name: Show image size comparison
      if: steps.registry-auth.outputs.should-push == 'true'
      run: |
        echo "=== Image Size Comparison ==="
        echo "Standard image: ${{ env.LATEST_IMAGE }}"
        docker images ${{ env.LATEST_IMAGE }} || echo "Standard image not found locally"
        echo ""
        echo "Optimized image: ${{ env.SLIM_IMAGE }}"
        docker images ${{ env.SLIM_IMAGE }} || echo "Optimized image not found locally"
        echo ""

        # 计算压缩比例
        STANDARD_SIZE=$(docker images ${{ env.LATEST_IMAGE }} --format "{{.Size}}" 2>/dev/null | head -n1)
        SLIM_SIZE=$(docker images ${{ env.SLIM_IMAGE }} --format "{{.Size}}" 2>/dev/null | head -n1)

        if [[ -n "$STANDARD_SIZE" && -n "$SLIM_SIZE" ]]; then
          echo "📊 Standard image size: $STANDARD_SIZE"
          echo "📊 Optimized image size: $SLIM_SIZE"

          # 尝试计算压缩比例（如果可能）
          if command -v python3 >/dev/null 2>&1; then
            STANDARD_MB=$(echo "$STANDARD_SIZE" | sed 's/MB//' | sed 's/GB/*1000/' | bc 2>/dev/null || echo "0")
            SLIM_MB=$(echo "$SLIM_SIZE" | sed 's/MB//' | sed 's/GB/*1000/' | bc 2>/dev/null || echo "0")
            if [[ "$STANDARD_MB" != "0" && "$SLIM_MB" != "0" ]]; then
              REDUCTION=$(python3 -c "print(f'{($STANDARD_MB - $SLIM_MB) / $STANDARD_MB * 100:.1f}%')" 2>/dev/null || echo "N/A")
              echo "📊 Size reduction: $REDUCTION"
            fi
          fi
        else
          echo "⚠️ Could not retrieve image sizes for comparison"
        fi