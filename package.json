{"name": "modelsentry", "version": "1.0.0", "description": "A lightweight monitoring tool for tracking AI model changes across multiple providers", "main": "src/server.ts", "scripts": {"dev": "bun run --watch src/server.ts", "start": "bun run src/server.ts", "build": "bun build src/server.ts --outdir dist --target bun --minify", "type-check": "bun run tsc --noEmit", "test": "echo 'No tests specified' && exit 0", "docker:build": "docker build -t modelsentry:latest .", "docker:run": "docker run --rm -p 3000:3000 modelsentry:latest", "compose:up": "docker-compose up -d", "compose:down": "docker-compose down"}, "keywords": ["ai", "monitoring", "models", "webhook", "notifications"], "author": "mgrsc <<EMAIL>>", "license": "MIT", "dependencies": {"@types/node": "^20.0.0"}, "devDependencies": {"@types/bun": "^1.2.16", "typescript": "^5.0.0"}, "engines": {"bun": ">=1.0.0"}}