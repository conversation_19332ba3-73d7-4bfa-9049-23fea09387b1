# ModelSentry Environment Variables Example
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3000

# AI Provider API Keys
OPENAI_API_KEY=sk-your-openai-key-here
CLAUDE_API_KEY=sk-ant-your-claude-key-here
GEMINI_API_KEY=your-gemini-key-here
GROQ_API_KEY=gsk_your-groq-key-here
CEREBRAS_API_KEY=your-cerebras-key-here
COHERE_API_KEY=your-cohere-key-here
XAI_API_KEY=your-xai-key-here
MISTRAL_API_KEY=your-mistral-key-here
SAMBANOVA_API_KEY=your-sambanova-key-here
TOGETHER_API_KEY=your-together-key-here
CN_360_API_KEY=your-360-key-here
SILICONFLOW_API_KEY=your-siliconflow-key-here
BAICHUAN_API_KEY=your-baichuan-key-here
DEEPSEEK_API_KEY=your-deepseek-key-here
STEPFUN_API_KEY=your-stepfun-key-here

# Webhook URLs for Notifications
WEWORK_BOT_KEY_CHANGES=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-wework-key
LARK_BOT_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-lark-key

# Optional: Custom webhook URLs
CUSTOM_WEBHOOK_URL=https://your-custom-webhook.com/notify
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook
